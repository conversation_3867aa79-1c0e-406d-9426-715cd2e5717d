'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { 
  ArrowLeft, 
  Edit, 
  Trash2, 
  User, 
  Building, 
  Phone, 
  Mail, 
  Calendar,
  Percent,
  MessageSquare,
  Clock
} from 'lucide-react';
import Link from 'next/link';

// Types
interface Lead {
  id: string;
  customerId: string;
  executiveId: string;
  leadDate: string;
  contactPerson?: string;
  contactPhone?: string;
  status: string;
  prospectPercentage?: number;
  followUpDate?: string;
  nextVisitDate?: string;
  remarks?: string;
  createdAt: string;
  updatedAt: string;
  customer: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
    city?: string;
    address?: string;
  };
  executive: {
    id: string;
    name: string;
    email?: string;
    designation?: string;
  };
}

// Status badge colors
const getStatusColor = (status: string) => {
  switch (status) {
    case 'NEW': return 'bg-blue-100 text-blue-800';
    case 'CONTACTED': return 'bg-yellow-100 text-yellow-800';
    case 'QUALIFIED': return 'bg-green-100 text-green-800';
    case 'PROPOSAL': return 'bg-purple-100 text-purple-800';
    case 'NEGOTIATION': return 'bg-orange-100 text-orange-800';
    case 'CLOSED_WON': return 'bg-green-100 text-green-800';
    case 'CLOSED_LOST': return 'bg-red-100 text-red-800';
    default: return 'bg-gray-100 text-gray-800';
  }
};

export default function LeadDetailPage() {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const [lead, setLead] = useState<Lead | null>(null);
  const [loading, setLoading] = useState(true);

  const leadId = params.id as string;

  // Fetch lead data
  useEffect(() => {
    const fetchLead = async () => {
      try {
        setLoading(true);
        
        const response = await fetch(`/api/sales/leads/${leadId}`, {
          credentials: 'include',
        });

        if (!response.ok) {
          if (response.status === 404) {
            toast({
              title: 'Error',
              description: 'Lead not found.',
              variant: 'destructive',
            });
            router.push('/leads');
            return;
          }
          throw new Error('Failed to fetch lead');
        }

        const result = await response.json();
        
        if (result.success) {
          setLead(result.data);
        } else {
          throw new Error('Failed to load lead');
        }
      } catch (error) {
        console.error('Error fetching lead:', error);
        toast({
          title: 'Error',
          description: 'Failed to load lead. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setLoading(false);
      }
    };

    if (leadId) {
      fetchLead();
    }
  }, [leadId, toast, router]);

  // Delete lead
  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this lead?')) {
      return;
    }

    try {
      const response = await fetch(`/api/sales/leads/${leadId}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to delete lead');
      }

      toast({
        title: 'Success',
        description: 'Lead deleted successfully.',
      });

      router.push('/leads');
    } catch (error) {
      console.error('Error deleting lead:', error);
      toast({
        title: 'Error',
        description: 'Failed to delete lead. Please try again.',
        variant: 'destructive',
      });
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-8 w-64" />
            <Skeleton className="h-4 w-96" />
          </CardHeader>
        </Card>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-4 w-4" />
                  <Skeleton className="h-4 w-48" />
                </div>
              ))}
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
            </CardHeader>
            <CardContent className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-4 w-4" />
                  <Skeleton className="h-4 w-48" />
                </div>
              ))}
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!lead) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900">Lead not found</h2>
        <p className="text-gray-600 mt-2">The lead you're looking for doesn't exist.</p>
        <Button asChild className="mt-4">
          <Link href="/leads">Back to Leads</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader className="bg-primary text-primary-foreground">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-white">Lead Details</CardTitle>
              <CardDescription className="text-primary-foreground/80">
                {lead.customer.name} - {format(new Date(lead.leadDate), 'MMM dd, yyyy')}
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <Button asChild variant="secondary" size="sm">
                <Link href={`/leads/${lead.id}/edit`}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Link>
              </Button>
              <Button 
                onClick={handleDelete}
                variant="destructive" 
                size="sm"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </Button>
              <Button asChild variant="outline" size="sm">
                <Link href="/leads">
                  <ArrowLeft className="mr-2 h-4 w-4" />
                  Back
                </Link>
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Lead Information */}
        <Card>
          <CardHeader>
            <CardTitle className="text-black">Lead Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              <Calendar className="h-4 w-4 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500">Lead Date</p>
                <p className="font-medium">{format(new Date(lead.leadDate), 'MMM dd, yyyy')}</p>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Badge className={getStatusColor(lead.status)}>
                {lead.status.replace('_', ' ')}
              </Badge>
            </div>

            {lead.contactPerson && (
              <div className="flex items-center space-x-3">
                <User className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Contact Person</p>
                  <p className="font-medium">{lead.contactPerson}</p>
                </div>
              </div>
            )}

            {lead.contactPhone && (
              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Contact Phone</p>
                  <p className="font-medium">{lead.contactPhone}</p>
                </div>
              </div>
            )}

            {lead.prospectPercentage !== null && lead.prospectPercentage !== undefined && (
              <div className="flex items-center space-x-3">
                <Percent className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Prospect Percentage</p>
                  <p className="font-medium">{lead.prospectPercentage}%</p>
                </div>
              </div>
            )}

            {lead.followUpDate && (
              <div className="flex items-center space-x-3">
                <Clock className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Follow Up Date</p>
                  <p className="font-medium">{format(new Date(lead.followUpDate), 'MMM dd, yyyy')}</p>
                </div>
              </div>
            )}

            {lead.nextVisitDate && (
              <div className="flex items-center space-x-3">
                <Calendar className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Next Visit Date</p>
                  <p className="font-medium">{format(new Date(lead.nextVisitDate), 'MMM dd, yyyy')}</p>
                </div>
              </div>
            )}

            {lead.remarks && (
              <div className="flex items-start space-x-3">
                <MessageSquare className="h-4 w-4 text-gray-500 mt-1" />
                <div>
                  <p className="text-sm text-gray-500">Remarks</p>
                  <p className="font-medium whitespace-pre-wrap">{lead.remarks}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Customer & Executive Information */}
        <div className="space-y-6">
          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-black">Customer Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <Building className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Customer Name</p>
                  <p className="font-medium">{lead.customer.name}</p>
                </div>
              </div>

              {lead.customer.email && (
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="font-medium">{lead.customer.email}</p>
                  </div>
                </div>
              )}

              {lead.customer.phone && (
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Phone</p>
                    <p className="font-medium">{lead.customer.phone}</p>
                  </div>
                </div>
              )}

              {lead.customer.city && (
                <div className="flex items-center space-x-3">
                  <Building className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">City</p>
                    <p className="font-medium">{lead.customer.city}</p>
                  </div>
                </div>
              )}

              {lead.customer.address && (
                <div className="flex items-start space-x-3">
                  <Building className="h-4 w-4 text-gray-500 mt-1" />
                  <div>
                    <p className="text-sm text-gray-500">Address</p>
                    <p className="font-medium">{lead.customer.address}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Executive Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-black">Executive Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center space-x-3">
                <User className="h-4 w-4 text-gray-500" />
                <div>
                  <p className="text-sm text-gray-500">Executive Name</p>
                  <p className="font-medium">{lead.executive.name}</p>
                </div>
              </div>

              {lead.executive.designation && (
                <div className="flex items-center space-x-3">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Designation</p>
                    <p className="font-medium">{lead.executive.designation}</p>
                  </div>
                </div>
              )}

              {lead.executive.email && (
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="font-medium">{lead.executive.email}</p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Metadata */}
      <Card>
        <CardHeader>
          <CardTitle className="text-black">Metadata</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">Created At</p>
              <p className="font-medium">{format(new Date(lead.createdAt), 'MMM dd, yyyy HH:mm')}</p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Last Updated</p>
              <p className="font-medium">{format(new Date(lead.updatedAt), 'MMM dd, yyyy HH:mm')}</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
