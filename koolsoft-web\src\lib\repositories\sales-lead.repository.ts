import { PrismaClient, Prisma } from '@/generated/prisma';
import { BaseRepository } from './base.repository';
import { PrismaRepository } from './prisma.repository';

/**
 * Sales Lead Repository
 *
 * This repository handles database operations for the Sales Lead entity.
 * It provides methods for CRUD operations and specialized queries.
 */
export class SalesLeadRepository extends PrismaRepository<
  Prisma.sales_leadsGetPayload<{
    include: {
      customer: true;
      executive: true;
    };
  }>,
  string,
  Prisma.sales_leadsCreateInput,
  Prisma.sales_leadsUpdateInput
> {
  constructor(prismaClient?: PrismaClient) {
    super('sales_leads');
    if (prismaClient) {
      this.prisma = prismaClient;
    }
  }

  protected createTransactionRepository(tx: any): SalesLeadRepository {
    return new SalesLeadRepository(tx);
  }

  /**
   * Find sales leads with filtering, pagination, and sorting
   * @param filters Filter criteria
   * @returns Promise resolving to paginated sales leads with metadata
   */
  async findWithFilters(filters: {
    customerId?: string;
    executiveId?: string;
    status?: string;
    startDate?: Date;
    endDate?: Date;
    search?: string;
    skip?: number;
    take?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }) {
    const {
      customerId,
      executiveId,
      status,
      startDate,
      endDate,
      search,
      skip = 0,
      take = 10,
      sortBy = 'leadDate',
      sortOrder = 'desc',
    } = filters;

    // Build where clause
    const where: Prisma.sales_leadsWhereInput = {};

    if (customerId) {
      where.customerId = customerId;
    }

    if (executiveId) {
      where.executiveId = executiveId;
    }

    if (status) {
      where.status = status;
    }

    if (startDate || endDate) {
      where.leadDate = {};
      if (startDate) {
        where.leadDate.gte = startDate;
      }
      if (endDate) {
        where.leadDate.lte = endDate;
      }
    }

    if (search) {
      where.OR = [
        {
          customer: {
            name: {
              contains: search,
              mode: 'insensitive',
            },
          },
        },
        {
          contactPerson: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          contactPhone: {
            contains: search,
            mode: 'insensitive',
          },
        },
        {
          remarks: {
            contains: search,
            mode: 'insensitive',
          },
        },
      ];
    }

    // Build order by clause
    let orderBy: Prisma.sales_leadsOrderByWithRelationInput = {};
    if (sortBy === 'customer') {
      orderBy = { customer: { name: sortOrder } };
    } else if (sortBy === 'executive') {
      orderBy = { executive: { name: sortOrder } };
    } else if (sortBy === 'leadDate') {
      orderBy = { leadDate: sortOrder };
    } else if (sortBy === 'status') {
      orderBy = { status: sortOrder };
    } else if (sortBy === 'prospectPercentage') {
      orderBy = { prospectPercentage: sortOrder };
    } else if (sortBy === 'followUpDate') {
      orderBy = { followUpDate: sortOrder };
    } else if (sortBy === 'nextVisitDate') {
      orderBy = { nextVisitDate: sortOrder };
    } else {
      orderBy = { leadDate: sortOrder }; // Default fallback
    }

    // Execute queries
    const [leads, total] = await Promise.all([
      this.model.findMany({
        where,
        include: {
          customer: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
              city: true,
            },
          },
          executive: {
            select: {
              id: true,
              name: true,
              email: true,
              designation: true,
            },
          },
        },
        orderBy,
        skip,
        take,
      }),
      this.model.count({ where }),
    ]);

    return {
      data: leads,
      pagination: {
        total,
        skip,
        take,
        pages: Math.ceil(total / take),
      },
    };
  }

  /**
   * Find sales leads by customer ID
   * @param customerId Customer ID
   * @param options Query options
   * @returns Promise resolving to sales leads
   */
  async findByCustomerId(
    customerId: string,
    options?: {
      skip?: number;
      take?: number;
      includeRelations?: boolean;
    }
  ) {
    const { skip = 0, take = 10, includeRelations = true } = options || {};

    return this.model.findMany({
      where: { customerId },
      include: includeRelations
        ? {
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                city: true,
              },
            },
            executive: {
              select: {
                id: true,
                name: true,
                email: true,
                designation: true,
              },
            },
          }
        : undefined,
      orderBy: { leadDate: 'desc' },
      skip,
      take,
    });
  }

  /**
   * Find sales leads by executive ID
   * @param executiveId Executive ID
   * @param options Query options
   * @returns Promise resolving to sales leads
   */
  async findByExecutiveId(
    executiveId: string,
    options?: {
      skip?: number;
      take?: number;
      includeRelations?: boolean;
    }
  ) {
    const { skip = 0, take = 10, includeRelations = true } = options || {};

    return this.model.findMany({
      where: { executiveId },
      include: includeRelations
        ? {
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                city: true,
              },
            },
            executive: {
              select: {
                id: true,
                name: true,
                email: true,
                designation: true,
              },
            },
          }
        : undefined,
      orderBy: { leadDate: 'desc' },
      skip,
      take,
    });
  }

  /**
   * Find sales leads by status
   * @param status Lead status
   * @param options Query options
   * @returns Promise resolving to sales leads
   */
  async findByStatus(
    status: string,
    options?: {
      skip?: number;
      take?: number;
      includeRelations?: boolean;
    }
  ) {
    const { skip = 0, take = 10, includeRelations = true } = options || {};

    return this.model.findMany({
      where: { status },
      include: includeRelations
        ? {
            customer: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                city: true,
              },
            },
            executive: {
              select: {
                id: true,
                name: true,
                email: true,
                designation: true,
              },
            },
          }
        : undefined,
      orderBy: { leadDate: 'desc' },
      skip,
      take,
    });
  }

  /**
   * Get sales leads statistics
   * @param filters Optional filters
   * @returns Promise resolving to statistics
   */
  async getStatistics(filters?: {
    customerId?: string;
    executiveId?: string;
    startDate?: Date;
    endDate?: Date;
  }) {
    const { customerId, executiveId, startDate, endDate } = filters || {};

    // Build where clause
    const where: Prisma.sales_leadsWhereInput = {};

    if (customerId) {
      where.customerId = customerId;
    }

    if (executiveId) {
      where.executiveId = executiveId;
    }

    if (startDate || endDate) {
      where.leadDate = {};
      if (startDate) {
        where.leadDate.gte = startDate;
      }
      if (endDate) {
        where.leadDate.lte = endDate;
      }
    }

    // Get statistics
    const [
      total,
      newLeads,
      qualifiedLeads,
      closedWonLeads,
      closedLostLeads,
      averageProspectPercentage,
    ] = await Promise.all([
      this.model.count({ where }),
      this.model.count({ where: { ...where, status: 'NEW' } }),
      this.model.count({ where: { ...where, status: 'QUALIFIED' } }),
      this.model.count({ where: { ...where, status: 'CLOSED_WON' } }),
      this.model.count({ where: { ...where, status: 'CLOSED_LOST' } }),
      this.model.aggregate({
        where: {
          ...where,
          prospectPercentage: { not: null },
        },
        _avg: {
          prospectPercentage: true,
        },
      }),
    ]);

    return {
      total,
      newLeads,
      qualifiedLeads,
      closedWonLeads,
      closedLostLeads,
      averageProspectPercentage: averageProspectPercentage._avg.prospectPercentage || 0,
      conversionRate: total > 0 ? (closedWonLeads / total) * 100 : 0,
    };
  }

  /**
   * Get leads requiring follow-up
   * @param date Date to check for follow-ups (defaults to today)
   * @returns Promise resolving to leads requiring follow-up
   */
  async getLeadsRequiringFollowUp(date?: Date) {
    const checkDate = date || new Date();
    checkDate.setHours(23, 59, 59, 999); // End of day

    return this.model.findMany({
      where: {
        followUpDate: {
          lte: checkDate,
        },
        status: {
          notIn: ['CLOSED_WON', 'CLOSED_LOST'],
        },
      },
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            city: true,
          },
        },
        executive: {
          select: {
            id: true,
            name: true,
            email: true,
            designation: true,
          },
        },
      },
      orderBy: { followUpDate: 'asc' },
    });
  }

  /**
   * Update lead status
   * @param id Lead ID
   * @param status New status
   * @param remarks Optional remarks
   * @returns Promise resolving to updated lead
   */
  async updateStatus(id: string, status: string, remarks?: string) {
    const updateData: Prisma.sales_leadsUpdateInput = {
      status,
      updatedAt: new Date(),
    };

    if (remarks) {
      updateData.remarks = remarks;
    }

    return this.model.update({
      where: { id },
      data: updateData,
      include: {
        customer: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            city: true,
          },
        },
        executive: {
          select: {
            id: true,
            name: true,
            email: true,
            designation: true,
          },
        },
      },
    });
  }
}
