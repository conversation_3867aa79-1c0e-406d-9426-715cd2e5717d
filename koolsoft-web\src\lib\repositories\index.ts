import { prisma } from '@/lib/prisma';
import { UserRepository } from './user.repository';
import { CustomerRepository } from './customer.repository';
import { ActivityLogRepository } from './activity-log.repository';
import { AMCContractRepository } from './amc-contract.repository';
import { AMCMachineRepository } from './amc-machine.repository';
import { AMCPaymentRepository } from './amc-payment.repository';
import { AMCServiceDateRepository } from './amc-service-date.repository';
import { AMCComponentRepository } from './amc-component.repository';
import { WarrantyRepository } from './warranty.repository';
import { WarrantyMachineRepository } from './warranty-machine.repository';
import { WarrantyComponentRepository } from './warranty-component.repository';
import { OutWarrantyRepository } from './out-warranty.repository';
import { HistoryCardRepository } from './history-card.repository';
import { LegacyDataRepository } from './legacy-data.repository';
import { ServiceReportRepository } from './service-report.repository';
import { ServiceDetailRepository } from './service-detail.repository';
import { ServiceScheduleRepository } from './service-schedule.repository';
import { SalesLeadRepository } from './sales-lead.repository';
import { SalesOpportunityRepository } from './sales-opportunity.repository';
import { SalesProspectRepository } from './sales-prospect.repository';
import { SalesOrderRepository } from './sales-order.repository';
import { getRepositoryFactory } from './repository.factory';


// Export repository interfaces and base classes
export * from './base.repository';
export * from './prisma.repository';

// Export specific repositories
export * from './user.repository';
export * from './customer.repository';
export * from './amc-contract.repository';
export * from './amc-machine.repository';
export * from './amc-payment.repository';
export * from './amc-service-date.repository';
export * from './amc-component.repository';
export * from './warranty.repository';
export * from './warranty-machine.repository';
export * from './warranty-component.repository';
export * from './out-warranty.repository';
export * from './reference-data.repository';
export * from './history-detail.repository';
export * from './history-card.repository';
export * from './visit-card.repository';
export * from './email.repository';
export * from './legacy-data.repository';
export * from './sales-lead.repository';
export * from './sales-opportunity.repository';
export * from './sales-prospect.repository';
export * from './sales-order.repository';
export * from './service-report.repository';
export * from './service-detail.repository';
export * from './service-schedule.repository';


// Export repository factory
export * from './repository.factory';

// Export compatibility layer for legacy repositories
export * from './compatibility';

export function getUserRepository() {
  return new UserRepository(prisma);
}

/**
 * Get the customer repository instance
 * @returns CustomerRepository instance
 */
export function getCustomerRepository() {
  return new CustomerRepository(prisma);
}

/**
 * Get the activity log repository instance
 * @returns ActivityLogRepository instance
 */
export function getActivityLogRepository() {
  return new ActivityLogRepository();
}

/**
 * Get the AMC contract repository instance
 * @returns AMCContractRepository instance
 */
export function getAMCContractRepository() {
  return getRepositoryFactory().getAMCContractRepository();
}

/**
 * Get the AMC machine repository instance
 * @returns AMCMachineRepository instance
 */
export function getAMCMachineRepository() {
  return getRepositoryFactory().getAMCMachineRepository();
}

/**
 * Get the AMC payment repository instance
 * @returns AMCPaymentRepository instance
 */
export function getAMCPaymentRepository() {
  return new AMCPaymentRepository(prisma);
}

/**
 * Get the warranty repository instance
 * @returns WarrantyRepository instance
 */
export function getWarrantyRepository() {
  return new WarrantyRepository(prisma);
}

/**
 * Get the warranty machine repository instance
 * @returns WarrantyMachineRepository instance
 */
export function getWarrantyMachineRepository() {
  return new WarrantyMachineRepository(prisma);
}

/**
 * Get the warranty component repository instance
 * @returns WarrantyComponentRepository instance
 */
export function getWarrantyComponentRepository() {
  return new WarrantyComponentRepository(prisma);
}

/**
 * Get the history card repository instance
 * @returns HistoryCardRepository instance
 */
export function getHistoryCardRepository() {
  return new HistoryCardRepository(prisma);
}

/**
 * Get the AMC service date repository instance
 * @returns AMCServiceDateRepository instance
 */
export function getAMCServiceDateRepository() {
  return new AMCServiceDateRepository(prisma);
}

/**
 * Get the AMC component repository instance
 * @returns AMCComponentRepository instance
 */
export function getAMCComponentRepository() {
  return new AMCComponentRepository(prisma);
}

/**
 * Get the legacy data repository instance
 * @returns LegacyDataRepository instance
 */
export function getLegacyDataRepository() {
  return new LegacyDataRepository(prisma);
}

/**
 * Get the service report repository instance
 * @returns ServiceReportRepository instance
 */
export function getServiceReportRepository() {
  return new ServiceReportRepository(prisma);
}

/**
 * Get the service detail repository instance
 * @returns ServiceDetailRepository instance
 */
export function getServiceDetailRepository() {
  return new ServiceDetailRepository(prisma);
}

/**
 * Get the service schedule repository instance
 * @returns ServiceScheduleRepository instance
 */
export function getServiceScheduleRepository() {
  return new ServiceScheduleRepository(prisma);
}

/**
 * Get the out-warranty repository instance
 * @returns OutWarrantyRepository instance
 */
export function getOutWarrantyRepository() {
  return new OutWarrantyRepository(prisma);
}

/**
 * Get the sales lead repository instance
 * @returns SalesLeadRepository instance
 */
export function getSalesLeadRepository() {
  return new SalesLeadRepository(prisma);
}

/**
 * Get the sales opportunity repository instance
 * @returns SalesOpportunityRepository instance
 */
export function getSalesOpportunityRepository() {
  return new SalesOpportunityRepository(prisma);
}

/**
 * Get the sales prospect repository instance
 * @returns SalesProspectRepository instance
 */
export function getSalesProspectRepository() {
  return new SalesProspectRepository(prisma);
}

/**
 * Get the sales order repository instance
 * @returns SalesOrderRepository instance
 */
export function getSalesOrderRepository() {
  return new SalesOrderRepository(prisma);
}